Create a responsive, fast-loading, modern restaurant website using Next.js and TailwindCSS to showcase the restaurant’s brand, menu, location, and contact options.
🧑‍🤝‍🧑 Target Users:

    General public seeking restaurant information.

    Customers wanting to view the menu or book a table.

    Mobile and desktop users.

🧩 Features:

1. Home Page

   Hero section with restaurant name, tagline, and call-to-action (CTA).

   Featured dishes or sections (carousel/grid).

   Short "About Us".

2. About Page

   Description of the restaurant’s story, values, and chef/team.

3. Menu Page

   Display categories (e.g., Starters, Main Course, Desserts, Drinks).

   Images, names, and prices of dishes.

   Filter by category (optional).

4. Reservation Page

   Simple form with: Name, Email, Phone, Date, Time, Number of Guests.

   Submit to an API or email service (like EmailJS, Formspree, etc.).

5. Contact Page

   Restaurant address, Google Maps embed.

   Phone number and email address.

   Contact form.

6. Navigation

   Responsive navbar with logo and links to all pages.

   Sticky or dynamic behavior on scroll.

7. Footer

   Social media links.

   Opening hours.

   Address.

📱 Responsive Design:

    Mobile-first design with TailwindCSS.

    Fully responsive on phones, tablets, and desktops.

⚙️ Tech Stack:

    Frontend: Next.js (App Router), TailwindCSS

    Forms: Local state or external API (e.g., EmailJS)

    Hosting: Vercel (default for Next.js)

🎨 Design Notes:

    Clean and modern design.

    Consistent typography and spacing.

    High-quality food images.

    Use of Tailwind’s utility classes for layout and styling.
